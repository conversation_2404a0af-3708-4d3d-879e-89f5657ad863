<script lang="ts" setup>
import type { V1ManageTrainBookingsPostRequestBody } from '@/api/api.model'
import { V1ManageSysUnitTerminalList, V1ManageTrainBookingsPost } from '@/api/api.req'
import { useAsyncData } from '@/hooks/useAsyncData'
import { useRouteQuery } from '@/hooks/useRouteQuery'
import { currentUserInfo } from '@/store/sysUser'
import dayjs, { Dayjs } from 'dayjs'
import { showToast } from 'vant'

const router = useRouter()
const query = useRouteQuery<{
  station?: string
  date?: string
}>()

const formState = reactive({
  station: query.station ?? '',
  date: query.date ? dayjs(query.date, 'YYYY-MM-DD').format(`MM-DD`).split('-') : [],
  startTime: formatTime(dayjs().add(1, 'm')),
  endTime: formatTime(dayjs().add(1, 'm')),
})

function formatTime(date: Dayjs | Date) {
  return dayjs(date).format('HH:mm').split(':')
}

const datePickerConfig = {
  minDate: dayjs().startOf('d').toDate(),
  maxDate: dayjs().startOf('d').add(6, 'd').toDate(),
}

const stationsApi = useAsyncData(async () => {
  const resp = await V1ManageSysUnitTerminalList()

  return resp.map((item) => ({
    ...item,
    text: item.unitName,
    value: item.unitId,
  }))
}, [])

watch(
  () => formState.startTime,
  () => {
    const start = dayjs.duration(+formState.startTime[0], 'm').add(+formState.startTime[1], 's')
    const end = dayjs.duration(+formState.endTime[0], 'm').add(+formState.endTime[1], 's')

    if (end.asSeconds() <= start.asSeconds()) {
      formState.endTime = formState.startTime.slice()
    }
  },
  { flush: 'post' },
)

fetchInitData()

async function fetchInitData() {
  await stationsApi.load()
}

async function onSubmit() {
  const data: V1ManageTrainBookingsPostRequestBody = {
    userId: currentUserInfo.value.id!,
    unitId: formState.station,
    startTime: formatTime(formState.startTime),
    endTime: formatTime(formState.endTime),
  }

  await V1ManageTrainBookingsPost(data)

  showToast({
    type: 'success',
    message: '预约成功',
  })

  router.back()

  function formatTime(t: string[]) {
    let d = dayjs()
    d = d.month(+formState.date[0] - 1)
    d = d.date(+formState.date[1])
    d = d.hour(+t[0])
    d = d.minute(+t[1])
    d = d.second(0)
    return d.format(`YYYY-MM-DD HH:mm:ss`) as any
  }
}
</script>

<template>
  <VanNavBar title="新增预约" left-arrow left-text="返回" @click-left="$router.back()" />
  <div class="px-4 pt-4">
    <CardBox class="">
      <van-cell-group>
        <PickerField v-model="formState.station" label="操作台" :columns="stationsApi.data.value" />
        <DatePickerField
          v-model="formState.date"
          label="日期"
          :columns-type="['month', 'day']"
          v-bind="datePickerConfig"
        />
        <TimePickerField v-model="formState.startTime" label="开始时间" />
        <TimePickerField v-model="formState.endTime" label="结束时间" />
      </van-cell-group>
    </CardBox>
  </div>
  <FixedButton @click="onSubmit" type="primary"> 提交 </FixedButton>
</template>

<style lang="less" scoped></style>
