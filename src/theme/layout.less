.layout-custom-home {
  .content-header,
  .content {
    background-color: #f6f6f6;
    .content-view {
      &::-webkit-scrollbar {
        width: 0;
      }
    }
  }
}

.page-content {
  height: calc(100vh - 50px);
  overflow: auto;

  &.page-content-padding {
    padding: 16px 16px 60px;
  }

  .van-pull-refresh__track {
    min-height: calc(100vh - 160px);
  }

  &.without-footer {
    height: calc(100vh - 50px);

    .van-pull-refresh__track {
      min-height: calc(100vh - 60px);
    }
  }
}
