<script lang="ts" setup>
import { useVModel } from '@vueuse/core'

export interface PopupFieldProps {
  label?: string
  placeholder?: string

  visible?: boolean
  lazyRender?: boolean

  disabled?: boolean

  content?: string
}

const props = defineProps<PopupFieldProps>()

const emit = defineEmits(['update:visible'])

const vShow = useVModel(props, 'visible', emit, {
  passive: true,
})

function open() {
  vShow.value = true
}
</script>

<template>
  <VanField
    is-link
    readonly
    :label="label"
    :model-value="content"
    :placeholder="placeholder || `请选择`"
    :disabled="disabled"
    @click="open()"
  />
  <VanPopup
    v-model:show="vShow"
    round
    position="bottom"
    :lazyRender="lazyRender ?? true"
    :z-index="999999"
  >
    <slot> </slot>
  </VanPopup>
</template>

<style lang="less" scoped></style>
