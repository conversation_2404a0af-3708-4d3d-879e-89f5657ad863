<script lang="ts" setup>
import { toArray } from '@/utils'
import type { PickerOption } from 'vant'
import type { PopupFieldProps } from './PopupFieldItem.vue'
import { watchImmediate } from '@vueuse/core'

export interface PickerFieldProps extends PopupFieldProps {
  modelValue?: string | number
  columns?: PickerOption[]
}

const props = defineProps<PickerFieldProps>()

const emit = defineEmits(['update:modelValue', 'update:visible'])

const state = reactive({
  visible: false,
  selected: [] as PickerOption[],
})

const content = computed(() => state.selected.at(0)?.text?.toString())

watchImmediate(
  () => [props.modelValue, props.columns],
  () => {
    const value = toArray(props.modelValue)

    state.selected =
      value
        .map((item) => props.columns?.find((opt) => opt.value === item)!)
        //
        .filter(Boolean) || []
  },
)

function onConfirm({ selectedOptions }: { selectedOptions: PickerOption[] }) {
  state.selected = selectedOptions

  const item = state.selected.at(0)
  emit('update:modelValue', item?.value)

  state.visible = false
}

function onCancel() {
  state.visible = false
}
</script>

<template>
  <PopupFieldItem
    :label="label"
    :placeholder="placeholder"
    :lazy-render="lazyRender"
    :disabled="disabled"
    :content="content"
    v-model:visible="state.visible"
  >
    <VanPicker
      :modelValue="state.selected.map((item) => item.value!)"
      :columns="columns"
      @confirm="onConfirm"
      @cancel="onCancel"
    >
    </VanPicker>
  </PopupFieldItem>
</template>

<style lang="less" scoped></style>
