<script lang="ts" setup>
interface Props {
  src: string
  title?: string
  poster?: string
  autoplay?: boolean
  controls?: boolean
  muted?: boolean
  loop?: boolean
  width?: string | number
  height?: string | number
  createTime?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  poster: '',
  autoplay: false,
  controls: true,
  muted: false,
  loop: false,
  width: '100%',
  height: 'auto',
  createTime: '',
})

const emit = defineEmits<{
  retry: []
}>()

const videoRef = ref<HTMLVideoElement>()
const isLoading = ref(true)
const hasError = ref(false)
const isPlaying = ref(false)
const isMuted = ref(false) // 跟踪静音状态

function onLoadStart() {
  console.log('Video loadstart event, src:', props.src)
  isLoading.value = true
  hasError.value = false
}

function onLoadedData() {
  console.log('Video loadeddata event')
  isLoading.value = false
}

function onError(event: Event) {
  console.error('Video error event:', event)
  console.error('Video error details:', (event.target as HTMLVideoElement)?.error)
  isLoading.value = false
  hasError.value = true
}

function onPlay() {
  isPlaying.value = true
}

function onPause() {
  isPlaying.value = false
}

function onVolumeChange() {
  if (videoRef.value) {
    isMuted.value = videoRef.value.muted
  }
}

function togglePlay() {
  if (!videoRef.value)
    return

  if (isPlaying.value) {
    videoRef.value.pause()
  }
  else {
    videoRef.value.play()
  }
}

// 格式化时间
function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 格式化创建时间
function formatCreateTime(createTime?: string): string {
  if (!createTime) {
    return new Date().toLocaleDateString()
  }

  try {
    // 尝试解析时间字符串
    const date = new Date(createTime)
    if (Number.isNaN(date.getTime())) {
      // 如果解析失败，返回当前时间
      return new Date().toLocaleDateString()
    }
    return date.toLocaleDateString()
  }
  catch {
    // 如果出错，返回当前时间
    return new Date().toLocaleDateString()
  }
}

const currentTime = ref(0)
const duration = ref(0)

function onTimeUpdate() {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime
  }
}

function onLoadedMetadata() {
  if (videoRef.value) {
    duration.value = videoRef.value.duration
  }
}

const progressPercentage = computed(() => {
  if (duration.value === 0)
    return 0
  return (currentTime.value / duration.value) * 100
})

// 监听src变化，重置状态
watch(() => props.src, (newSrc) => {
  console.log('Video src changed to:', newSrc)
  if (newSrc) {
    isLoading.value = true
    hasError.value = false
    isPlaying.value = false
    isMuted.value = props.muted
  }
  else {
    isLoading.value = false
    hasError.value = false
    isPlaying.value = false
    isMuted.value = props.muted
  }
}, { immediate: true })

// 监听muted属性变化
watch(() => props.muted, (newMuted) => {
  isMuted.value = newMuted
}, { immediate: true })
</script>

<template>
  <div class="video-player">
    <!-- 视频标题 -->
    <div v-if="title" class="video-header">
      <h3 class="video-title">
        {{ title }}
      </h3>
      <div class="video-meta">
        <span class="file-type">视频</span>
        <span class="duration">
          更新时间: {{ formatCreateTime(createTime) }}
        </span>
      </div>
    </div>

    <!-- 视频容器 -->
    <div class="video-container">
      <!-- 加载状态 -->
      <div v-if="isLoading && !hasError" class="video-loading">
        <VanLoading type="spinner" />
        <span>视频加载中...</span>
      </div>

      <!-- 错误状态 -->
      <div v-if="hasError" class="video-error">
        <VanIcon name="warning-o" />
        <span>视频加载失败</span>
        <VanButton size="small" @click="emit('retry')">
          重试
        </VanButton>
      </div>

      <!-- 视频播放器 -->
      <video
        v-show="!hasError && src"
        ref="videoRef"
        :src="src"
        :poster="poster"
        :autoplay="autoplay"
        :controls="controls"
        :muted="isMuted"
        :loop="loop"
        :width="width"
        :height="height"
        class="video-element"
        @loadstart="onLoadStart"
        @loadeddata="onLoadedData"
        @loadedmetadata="onLoadedMetadata"
        @error="onError"
        @play="onPlay"
        @pause="onPause"
        @timeupdate="onTimeUpdate"
        @volumechange="onVolumeChange"
      />

      <!-- 自定义控制栏（可选） -->
      <div v-if="!controls && !hasError && !isLoading" class="custom-controls">
        <VanButton
          icon="play"
          size="small"
          @click="togglePlay"
        >
          {{ isPlaying ? '暂停' : '播放' }}
        </VanButton>

        <div class="time-info">
          <span>{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</span>
        </div>

        <div class="progress-bar">
          <div class="progress-track">
            <div
              class="progress-fill"
              :style="{ width: `${progressPercentage}%` }"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.video-player {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.video-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;

  .video-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
  }

  .video-meta {
    display: flex;
    gap: 8px;
    font-size: 12px;
    color: #666;
    align-items: center;

    .file-type {
      background: #e8f4ff;
      color: #1989fa;
      padding: 2px 6px;
      border-radius: 4px;
    }
  }
}

.video-container {
  position: relative;
  width: 100%;
  min-height: 200px;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-loading,
.video-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #fff;
  background: rgba(0, 0, 0, 0.8);
  z-index: 10;

  .van-icon {
    font-size: 24px;
  }
}

.video-error {
  color: #ff4444;
}

.video-element {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: contain;
}

.custom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;

  .time-info {
    color: #fff;
    font-size: 12px;
    white-space: nowrap;
  }

  .progress-bar {
    flex: 1;

    .progress-track {
      height: 4px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 2px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: #1989fa;
        transition: width 0.1s ease;
      }
    }
  }
}
</style>
