<script lang="ts" setup>
import { computed } from 'vue';
import { useGlobSetting } from '/@/hooks/setting';


const globSetting = useGlobSetting();

// 使用环境变量配置，如果未配置则使用默认值
const previewBaseUrl = globSetting.previewUrl || 'http://kkfv-server-test.caas-cloud-test.geega.com';

const props = withDefaults(
  defineProps<{
    fileUrl?: string;
  }>(),
  {
    fileUrl: 'http://*************:9000/gaea/PRODUCT/20250208/c2e07358de3e40249534c629dc41f25e.xlsx',
  }
);
 // 生成可预览链接
const previewUrl = computed(() => {

  if (!props.fileUrl) return '';

  const encodedUrl = btoa(
    unescape(
      encodeURIComponent(
        JSON.stringify({
          url: props.fileUrl,
          moduleType: 9,
          down: false,
          waterMark: globSetting.title,
        })
      )
    )
  );
  return `${previewBaseUrl}/onlinePreview?params=${encodeURIComponent(encodedUrl)}`;
});


</script>

<template>
  <div class="h-100% w-100%">
    <iframe
      :src="previewUrl"
      v-if="previewUrl"
      class="h-600px w-100%"
      frameborder="0"
      marginheight="0"
      marginwidth="0"
      border="0"
      hspace="0"
      vspace="0"
      scrolling="yes"
    ></iframe>
  </div>
</template>
